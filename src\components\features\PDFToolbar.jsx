import { createSignal, createMemo, Show, splitProps, createEffect } from 'solid-js';
import { Button, Input, Dropdown, DropdownItem, DropdownDivider, Tooltip, toast } from '../ui';
import {
  ZoomIn,
  ZoomOut,
  Highlighter,
  Type,
  PenTool,
  Save,
  Download,
  RotateCcw,
  RotateCw,
  ChevronLeft,
  ChevronRight,
  Printer
} from 'lucide-solid';
import { AnnotationEditorType } from 'pdfjs-dist';
import { filesAPI } from '../../services/api';

export default function PDFToolbar(props) {

  const [ local ] = splitProps(props, ['store', 'paperId', 'originalFileName', 'onPdfSaved']);

  const [isSaving, setIsSaving] = createSignal(false);

  // 缩放级别选项
  const zoomLevels = [
    { value: '25', label: '25%' },
    { value: '50', label: '50%' },
    { value: '75', label: '75%' },
    { value: '100', label: '100%' },
    { value: '125', label: '125%' },
    { value: '150', label: '150%' },
    { value: '200', label: '200%' },
    { value: '300', label: '300%' },
    { value: 'page-fit', label: '适合页面' },
    { value: 'page-width', label: '适合宽度' },
    { value: 'auto', label: '自动' },
  ];

  const isHighlightActive = () => local.store.annotationEditorMode === AnnotationEditorType.HIGHLIGHT;
  const isTextActive = () => local.store.annotationEditorMode === AnnotationEditorType.FREETEXT;
  const isInkActive = () => local.store.annotationEditorMode === AnnotationEditorType.INK;
  const totalPages = () => local.store.numPages || 0;
  const pageNumber = () => local.store.pageNumber || 1;
  const scale = () => Math.round((local.store.scale || 1) * 100);

  // 工具栏操作函数
  const handleZoomIn = () => {
    local.store.pdfSlick?.viewer?.increaseScale();
  };

  const handleZoomOut = () => {
    local.store.pdfSlick?.viewer?.decreaseScale();
  };

  const handleZoomChange = (value) => {
    if (value === 'page-fit' || value === 'page-width' || value === 'auto') {
      local.store.pdfSlick?.viewer && (local.store.pdfSlick.viewer.currentScaleValue = value);
    } else {
      const scaleValue = parseInt(value) / 100;
      local.store.pdfSlick?.viewer && (local.store.pdfSlick.viewer.currentScale = scaleValue);
    }
  };

  const handlePageChange = (page) => {
    const pageNum = parseInt(page);
    if (pageNum >= 1 && pageNum <= totalPages()) {
      local.store.pdfSlick?.gotoPage(pageNum);
    }
  };

  const handlePrevPage = () => {
    if (pageNumber() > 1) {
      local.store.pdfSlick?.gotoPage(pageNumber() - 1);
    }
  };

  const handleNextPage = () => {
    if (pageNumber() < totalPages()) {
      local.store.pdfSlick?.gotoPage(pageNumber() + 1);
    }
  };



  // 通用的注释模式切换函数
  const toggleAnnotationMode = (mode) => {
    const currentMode = local.store.annotationEditorMode;
    const newMode = currentMode === mode ? AnnotationEditorType.NONE : mode;
    local.store.pdfSlick?.setAnnotationEditorMode(newMode);
  };

  const toggleHighlight = () => toggleAnnotationMode(AnnotationEditorType.HIGHLIGHT);
  const toggleText = () => toggleAnnotationMode(AnnotationEditorType.FREETEXT);
  const toggleInk = () => toggleAnnotationMode(AnnotationEditorType.INK);

  const handleSave = async () => {
    if (!local.paperId) {
      toast.error('无法保存：缺少文献ID');
      return;
    }

    if (isSaving()) {
      return; // 防止重复保存
    }

    try {
      setIsSaving(true);
      toast.info('正在保存PDF...');

      // 获取带注释的PDF数据
      const pdfData = await local.store.pdfSlick?.document?.saveDocument();
      if (!pdfData) {
        throw new Error('无法获取PDF数据');
      }

      // 创建文件对象
      const blob = new Blob([pdfData], { type: 'application/pdf' });
      const originalName = local.originalFileName || 'document.pdf';
      const file = new File([blob], originalName, {
        type: 'application/pdf',
      });

      // 上传到后端
      const response = await filesAPI.upload(local.paperId, file, 'origin');

      if (response.data.code === 200) {
        toast.success('PDF保存成功！');
        console.log('PDF saved to backend:', response.data);

        // 保存成功后触发回调，通知父组件重新初始化
        if (local.onPdfSaved) {
          // 延迟一点时间确保后端文件已更新
          setTimeout(() => {
            local.onPdfSaved();
          }, 500);
        }
      } else {
        throw new Error(response.data.message || '保存失败');
      }
    } catch (err) {
      console.error('Save failed:', err);
      toast.error(`保存失败: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownload = async () => {
    try {
      toast.info('正在准备下载...');

      const pdfData = await local.store.pdfSlick?.document?.saveDocument();
      if (!pdfData) {
        throw new Error('无法获取PDF数据');
      }

      const blob = new Blob([pdfData], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;

      // 使用原文件名或默认名称
      const originalName = local.originalFileName || 'document.pdf';
      const fileName = originalName.replace(/\.pdf$/i, '_annotated.pdf');
      a.download = fileName;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('下载完成！');
    } catch (err) {
      console.error('Download failed:', err);
      toast.error(`下载失败: ${err.message}`);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleRotateLeft = () => {
    // PDF.js 旋转功能
    const currentRotation = local.store.pdfSlick?.viewer?.pagesRotation || 0;
    local.store.pdfSlick?.viewer && (local.store.pdfSlick.viewer.pagesRotation = currentRotation - 90);
  };

  const handleRotateRight = () => {
    const currentRotation = local.store.pdfSlick?.viewer?.pagesRotation || 0;
    local.store.pdfSlick?.viewer && (local.store.pdfSlick.viewer.pagesRotation = currentRotation + 90);
  };

  return (
    <div class="flex items-center gap-1 p-2 bg-theme-secondary border-b border-theme-border shadow-sm">
      {/* 页面导航 */}
      <div class="flex items-center gap-1">
        <Tooltip content="上一页" palcement="bottom">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handlePrevPage}
            disabled={pageNumber() <= 1}
          >
            <ChevronLeft size={16} />
          </Button>
        </Tooltip>
        
        <div class="flex items-center gap-1 text-sm">
          <Input
            type="number"
            value={pageNumber()}
            onChange={(e) => handlePageChange(e.target.value)}
            class="w-16 text-center text-sm"
            min="1"
            max={totalPages()}
          />
          <span class="text-theme-muted">/ {totalPages()}</span>
        </div>
        
        <Tooltip content="下一页" palcement="bottom">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleNextPage}
            disabled={pageNumber() >= totalPages()}
          >
            <ChevronRight size={16} />
          </Button>
        </Tooltip>
      </div>

      <div class="w-px h-6 bg-theme-border mx-2" />

      {/* 缩放控制 */}
      <div class="flex items-center gap-1">
        <Tooltip content="缩小" palcement="bottom">
          <Button variant="ghost" size="sm" onClick={handleZoomOut}>
            <ZoomOut size={16} />
          </Button>
        </Tooltip>
        
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm" class="min-w-20">
              {scale()}%
            </Button>
          }
        >
          {zoomLevels.map(level => (
            <DropdownItem onClick={() => handleZoomChange(level.value)}>
              {level.label}
            </DropdownItem>
          ))}
        </Dropdown>
        
        <Tooltip content="放大">
          <Button variant="ghost" size="sm" onClick={handleZoomIn}>
            <ZoomIn size={16} />
          </Button>
        </Tooltip>
      </div>

      <div class="w-px h-6 bg-theme-border mx-2" />

      {/* 注释工具 */}
      <div class="flex items-center gap-1">
        <Tooltip content="高亮">
          <Button
            variant={isHighlightActive() ? "primary" : "ghost"}
            size="sm"
            onClick={toggleHighlight}
          >
            <Highlighter size={16} />
          </Button>
        </Tooltip>

        <Tooltip content="文本注释">
          <Button
            variant={isTextActive() ? "primary" : "ghost"}
            size="sm"
            onClick={toggleText}
          >
            <Type size={16} />
          </Button>
        </Tooltip>

        <Tooltip content="手绘">
          <Button
            variant={isInkActive() ? "primary" : "ghost"}
            size="sm"
            onClick={toggleInk}
          >
            <PenTool size={16} />
          </Button>
        </Tooltip>
      </div>

      <div class="w-px h-6 bg-theme-border mx-2" />

      {/* 旋转工具 */}
      <div class="flex items-center gap-1">
        <Tooltip content="逆时针旋转">
          <Button variant="ghost" size="sm" onClick={handleRotateLeft}>
            <RotateCcw size={16} />
          </Button>
        </Tooltip>
        
        <Tooltip content="顺时针旋转">
          <Button variant="ghost" size="sm" onClick={handleRotateRight}>
            <RotateCw size={16} />
          </Button>
        </Tooltip>
      </div>

      <div class="flex-1" />

      {/* 右侧操作 */}
      <div class="flex items-center gap-1">
        <Tooltip content="打印">
          <Button variant="ghost" size="sm" onClick={handlePrint}>
            <Printer size={16} />
          </Button>
        </Tooltip>

        <Tooltip content="下载">
          <Button variant="ghost" size="sm" onClick={handleDownload}>
            <Download size={16} />
          </Button>
        </Tooltip>

        <Tooltip content="保存到服务器">
          <Button
            variant="primary"
            size="sm"
            onClick={handleSave}
            loading={isSaving()}
            disabled={isSaving() || !local.paperId}
          >
            <Save size={16} />
          </Button>
        </Tooltip>
      </div>
    </div>
  );
}
