/**
 * 简单的PDF功能测试工具
 * 用于验证重构后的PDF文本选择和注释功能
 */

export const testPdfFunctionality = () => {
  console.log('🔍 开始测试PDF功能...');

  // 检查PDF容器
  const pdfContainer = document.querySelector('.pdfSlickContainer') || document.querySelector('.PDFSlickViewer');
  console.log('PDF容器:', pdfContainer);

  // 检查PDF文本层
  const textLayer = document.querySelector('.textLayer');
  if (textLayer) {
    const textElements = textLayer.querySelectorAll('span');
    console.log(`✅ 找到PDF文本层，包含 ${textElements.length} 个文本元素`);
    console.log('文本层样式:', {
      pointerEvents: getComputedStyle(textLayer).pointerEvents,
      userSelect: getComputedStyle(textLayer).userSelect,
      zIndex: getComputedStyle(textLayer).zIndex
    });

    // 测试文本选择
    if (textElements.length > 0) {
      try {
        const firstElement = textElements[0];
        console.log('第一个文本元素:', {
          text: firstElement.textContent,
          pointerEvents: getComputedStyle(firstElement).pointerEvents,
          userSelect: getComputedStyle(firstElement).userSelect
        });

        const selection = window.getSelection();
        selection.removeAllRanges();

        const range = document.createRange();
        range.selectNodeContents(firstElement);
        selection.addRange(range);

        const selectedText = selection.toString();
        if (selectedText.length > 0) {
          console.log(`✅ 文本选择功能正常，选中文本: "${selectedText}"`);
          return true;
        } else {
          console.log('⚠️ 文本选择功能可能有问题 - 无法选中文本');
          return false;
        }

        selection.removeAllRanges();
      } catch (error) {
        console.error('❌ 文本选择测试失败:', error);
        return false;
      }
    }
  } else {
    console.log('⚠️ 未找到PDF文本层，请确保PDF已完全加载');
    return false;
  }

  // 检查注释层
  const annotationLayer = document.querySelector('.annotationLayer');
  if (annotationLayer) {
    console.log('✅ 找到PDF注释层');
  } else {
    console.log('⚠️ 未找到PDF注释层');
  }

  // 检查PDF画布
  const canvas = document.querySelector('.pdfViewer canvas');
  if (canvas) {
    console.log('✅ 找到PDF画布');
  } else {
    console.log('⚠️ 未找到PDF画布');
  }

  console.log('🔍 PDF功能测试完成');
  return false;
};

// 在开发环境下挂载到window对象
if (import.meta.env.DEV) {
  window.testPdfFunctionality = testPdfFunctionality;
  console.log('💡 在控制台输入 testPdfFunctionality() 来测试PDF功能');
}
