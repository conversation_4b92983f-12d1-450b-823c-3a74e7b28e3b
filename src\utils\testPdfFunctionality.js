/**
 * 简单的PDF功能测试工具
 * 用于验证重构后的PDF文本选择和注释功能
 */

export const testPdfFunctionality = () => {
  console.log('🔍 开始测试PDF功能...');
  
  // 检查PDF文本层
  const textLayer = document.querySelector('.textLayer');
  if (textLayer) {
    const textElements = textLayer.querySelectorAll('span');
    console.log(`✅ 找到PDF文本层，包含 ${textElements.length} 个文本元素`);
    
    // 测试文本选择
    if (textElements.length > 0) {
      try {
        const selection = window.getSelection();
        selection.removeAllRanges();
        
        const range = document.createRange();
        range.selectNodeContents(textElements[0]);
        selection.addRange(range);
        
        const selectedText = selection.toString();
        if (selectedText.length > 0) {
          console.log(`✅ 文本选择功能正常，选中文本: "${selectedText}"`);
        } else {
          console.log('⚠️ 文本选择功能可能有问题');
        }
        
        selection.removeAllRanges();
      } catch (error) {
        console.error('❌ 文本选择测试失败:', error);
      }
    }
  } else {
    console.log('⚠️ 未找到PDF文本层，请确保PDF已完全加载');
  }
  
  // 检查注释层
  const annotationLayer = document.querySelector('.annotationLayer');
  if (annotationLayer) {
    console.log('✅ 找到PDF注释层');
  } else {
    console.log('⚠️ 未找到PDF注释层');
  }
  
  // 检查PDF画布
  const canvas = document.querySelector('.pdfViewer canvas');
  if (canvas) {
    console.log('✅ 找到PDF画布');
  } else {
    console.log('⚠️ 未找到PDF画布');
  }
  
  console.log('🔍 PDF功能测试完成');
};

/**
 * 测试文献列表滚动功能
 */
export const testScrolling = () => {
  console.log('🔍 测试文献列表滚动功能...');

  const mainContent = document.querySelector('main');
  const scrollContainer = document.querySelector('main .overflow-y-auto');
  const paperGrid = document.querySelector('main .overflow-y-auto > div');

  if (!mainContent || !scrollContainer || !paperGrid) {
    console.log('⚠️ 未找到必要的DOM元素');
    console.log('mainContent:', !!mainContent);
    console.log('scrollContainer:', !!scrollContainer);
    console.log('paperGrid:', !!paperGrid);
    return;
  }

  const mainRect = mainContent.getBoundingClientRect();
  const scrollRect = scrollContainer.getBoundingClientRect();
  const gridRect = paperGrid.getBoundingClientRect();

  console.log('📏 布局信息:');
  console.log('主容器高度:', mainRect.height);
  console.log('滚动容器高度:', scrollRect.height);
  console.log('内容高度:', gridRect.height);
  console.log('是否需要滚动:', gridRect.height > scrollRect.height);
  console.log('滚动容器样式:', {
    overflow: getComputedStyle(scrollContainer).overflow,
    overflowY: getComputedStyle(scrollContainer).overflowY,
    height: getComputedStyle(scrollContainer).height,
    maxHeight: getComputedStyle(scrollContainer).maxHeight
  });

  // 测试滚动功能
  if (gridRect.height > scrollRect.height) {
    console.log('🔄 测试滚动功能...');
    const originalScrollTop = scrollContainer.scrollTop;

    // 滚动到底部
    scrollContainer.scrollTop = scrollContainer.scrollHeight;
    setTimeout(() => {
      if (scrollContainer.scrollTop > originalScrollTop) {
        console.log('✅ 滚动功能正常');
      } else {
        console.log('❌ 滚动功能异常');
      }

      // 恢复原始位置
      scrollContainer.scrollTop = originalScrollTop;
    }, 100);
  } else {
    console.log('ℹ️ 内容高度不足，无需滚动');
  }
};

// 在开发环境下挂载到window对象
if (import.meta.env.DEV) {
  window.testPdfFunctionality = testPdfFunctionality;
  window.testScrolling = testScrolling;
  console.log('💡 在控制台输入以下命令来测试功能：');
  console.log('   - testPdfFunctionality() 测试PDF文本选择功能');
  console.log('   - testScrolling() 测试文献列表滚动功能');
  console.log('🔧 滚动优化说明：');
  console.log('   1. 添加min-h-0约束确保flex容器正确计算高度');
  console.log('   2. 优化overflow-y-auto容器的高度约束');
  console.log('   3. 确保分页组件不影响滚动区域');
}
