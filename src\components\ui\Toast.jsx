import { createSignal, Show, onMount, splitProps, For } from 'solid-js';
import { CheckCircle, AlertCircle, XCircle, Info, X } from 'lucide-solid';

// Toast 容器组件
const ToastContainer = () => {
  return (
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <For each={toasts()}>
        {(toast) => (
          <Toast
            key={toast.id}
            type={toast.type}
            title={toast.title}
            message={toast.message}
            duration={toast.duration}
            closable={toast.closable}
            onClose={() => removeToast(toast.id)}
          />
        )}
      </For>
    </div>
  );
};

// 单个 Toast 组件
const Toast = (props) => {
  const [local, others] = splitProps(props, [
    'type',
    'title',
    'message',
    'duration',
    'closable',
    'onClose',
    'class'
  ]);

  const [visible, setVisible] = createSignal(true);

  const type = local.type || 'info';
  const duration = local.duration || 4000;
  const closable = local.closable !== false;

  const types = {
    success: {
      icon: CheckCircle,
      bgColor: 'bg-theme-success',
      borderColor: 'border-theme-success',
      iconColor: 'text-theme-success',
      titleColor: 'text-theme-primary',
      messageColor: 'text-theme-secondary'
    },
    error: {
      icon: XCircle,
      bgColor: 'bg-theme-error',
      borderColor: 'border-theme-error',
      iconColor: 'text-theme-error',
      titleColor: 'text-theme-primary',
      messageColor: 'text-theme-secondary'
    },
    warning: {
      icon: AlertCircle,
      bgColor: 'bg-theme-warning',
      borderColor: 'border-theme-warning',
      iconColor: 'text-theme-warning',
      titleColor: 'text-theme-primary',
      messageColor: 'text-theme-secondary'
    },
    info: {
      icon: Info,
      bgColor: 'bg-theme-info',
      borderColor: 'border-theme-info',
      iconColor: 'text-theme-info',
      titleColor: 'text-theme-primary',
      messageColor: 'text-theme-secondary'
    }
  };

  const config = types[type];
  const IconComponent = config.icon;

  const handleClose = () => {
    setVisible(false);
    if (local.onClose) {
      local.onClose();
    }
  };

  // 自动关闭
  onMount(() => {
    if (duration > 0) {
      setTimeout(() => {
        handleClose();
      }, duration);
    }
  });

  return (
    <Show when={visible()}>
      <div 
        class={`
          ${config.bgColor} ${config.borderColor}
          border rounded-lg p-4 shadow-theme-lg max-w-sm w-full
          transform transition-all duration-300 ease-in-out
          ${local.class || ''}
        `}
        {...others}
      >
        <div class="flex items-start">
          <div class={`flex-shrink-0 ${config.iconColor}`}>
            <IconComponent size={20} />
          </div>
          
          <div class="ml-3 flex-1">
            {local.title && (
              <h4 class={`text-sm font-medium ${config.titleColor}`}>
                {local.title}
              </h4>
            )}
            {local.message && (
              <p class={`text-sm ${config.messageColor} ${local.title ? 'mt-1' : ''}`}>
                {local.message}
              </p>
            )}
          </div>
          
          {closable && (
            <div class="ml-4 flex-shrink-0">
              <button
                onClick={handleClose}
                class={`inline-flex rounded-md p-1.5 ${config.iconColor} hover:bg-theme-muted hover:bg-opacity-20 transition-colors`}
              >
                <X size={16} />
              </button>
            </div>
          )}
        </div>
      </div>
    </Show>
  );
};

// Toast 管理器（简化版本，实际项目中可能需要更复杂的状态管理）
const [toasts, setToasts] = createSignal([]);

const addToast = (toast) => {
  const id = Date.now();
  const newToast = { ...toast, id };
  setToasts(prev => [...prev, newToast]);
  
  // 自动移除
  setTimeout(() => {
    removeToast(id);
  }, toast.duration || 4000);
  
  return id;
};

const removeToast = (id) => {
  setToasts(prev => prev.filter(toast => toast.id !== id));
};

// 便捷方法
const toast = {
  success: (message, options = {}) => addToast({ type: 'success', message, ...options }),
  error: (message, options = {}) => addToast({ type: 'error', message, ...options }),
  warning: (message, options = {}) => addToast({ type: 'warning', message, ...options }),
  info: (message, options = {}) => addToast({ type: 'info', message, ...options })
};

export { Toast as default, ToastContainer, toast };
