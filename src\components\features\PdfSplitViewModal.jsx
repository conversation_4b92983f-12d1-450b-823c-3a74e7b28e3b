import { createSignal, createMemo, onMount, onCleanup, createEffect, Show, For, splitProps } from 'solid-js';
import { X, Download, File, Image as ImageIcon, Plus, Trash2 } from 'lucide-solid';
import { Button, Badge, toast, Input } from '../ui';
import ImagePreview from '../ui/ImagePreview';
import MarkdownEditor from './MarkdownEditor';
import PdfViewer from './PdfViewer';

const PdfSplitViewModal = (props) => {
  const [local, others] = splitProps(props, [
    'open',
    'onClose',
    'paper',
    'originFile',
    'noteFiles',
    'imageFiles',
    'onFileDownload',
    'onFileCreated'
  ]);

  // 布局状态
  const [leftPanelWidth, setLeftPanelWidth] = createSignal(50); // 左侧面板宽度百分比
  const [isDragging, setIsDragging] = createSignal(false);
  const [isFullscreen, setIsFullscreen] = createSignal(false);

  // 图片预览状态
  const [showImagePreview, setShowImagePreview] = createSignal(false);
  const [previewImages, setPreviewImages] = createSignal([]);
  const [currentImageIndex, setCurrentImageIndex] = createSignal(0);

  // 编辑器状态
  const [editingFile, setEditingFile] = createSignal(null);
  const [rightPanelView, setRightPanelView] = createSignal('files'); // 'files' | 'editor'

  // 重命名状态
  const [renamingFile, setRenamingFile] = createSignal(null);
  const [newFileName, setNewFileName] = createSignal('');

  // PDF查看器重新创建key - 每次Modal打开时都生成新的key
  const [pdfViewerKey, setPdfViewerKey] = createSignal(0);

  let containerRef;

  // PDF文件URL - 简单的URL生成
  const pdfUrl = createMemo(() => {
    if (!local.paper || !local.originFile) return null;
    return `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper.id}/files/preview/${local.originFile.name}`;
  });

  // 拖拽调整面板宽度
  const startDrag = (e) => {
    e.preventDefault();
    setIsDragging(true);

    const handleMouseMove = (e) => {
      const containerWidth = containerRef.offsetWidth;
      const newWidth = Math.max(20, Math.min(80, (e.clientX / containerWidth) * 100));
      setLeftPanelWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef?.requestFullscreen?.();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen?.();
      setIsFullscreen(false);
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e) => {
    if (!local.open) return;

    switch (e.key) {
      case 'Escape':
        local.onClose?.();
        break;
      case 'f':
      case 'F':
        toggleFullscreen();
        break;
    }
  };

  // 图片预览处理
  const handleImagePreview = (file, imageFiles) => {
    const images = imageFiles.map(imgFile => ({
      src: `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper.id}/files/${imgFile.name}`,
      name: imgFile.name,
      alt: imgFile.name,
      file: imgFile
    }));

    const currentIndex = imageFiles.findIndex(imgFile => imgFile.name === file.name);
    setPreviewImages(images);
    setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);
    setShowImagePreview(true);
  };

  // 处理笔记文件编辑
  const handleNoteFileEdit = (file) => {
    // 检查文件类型是否支持编辑
    const editableExtensions = ['.md', '.txt', '.markdown', '.text'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (editableExtensions.includes(fileExtension)) {
      setEditingFile(file);
      setRightPanelView('editor');
    } else {
      toast.warning('该文件类型不支持在线编辑');
      // 如果不支持编辑，则下载文件
      local.onFileDownload?.(file);
    }
  };

  // 关闭编辑器
  const handleCloseEditor = () => {
    setEditingFile(null);
    setRightPanelView('files');
  };

  // 开始重命名文件
  const handleStartRename = (file) => {
    setRenamingFile(file);
    // 设置当前文件名（去掉扩展名）
    const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
    setNewFileName(nameWithoutExt);
  };

  // 取消重命名
  const handleCancelRename = () => {
    setRenamingFile(null);
    setNewFileName('');
  };

  // 处理重命名输入框的键盘事件
  const handleRenameKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleConfirmRename();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelRename();
    }
  };

  // 确认重命名
  const handleConfirmRename = async () => {
    const file = renamingFile();
    const trimmedName = newFileName().trim();

    if (!file || !trimmedName) {
      toast.warning('请输入有效的文件名');
      return;
    }

    // 获取原文件扩展名
    const originalExt = file.name.substring(file.name.lastIndexOf('.'));
    const finalNewName = trimmedName + originalExt;

    // 如果名称没有变化，直接取消
    if (finalNewName === file.name) {
      handleCancelRename();
      return;
    }

    try {
      // 调用重命名API
      const { filesAPI } = await import('../../services/api');
      await filesAPI.rename(local.paper?.id, file.name, finalNewName);

      // 重命名成功
      toast.success('文件重命名成功');

      // 通知父组件刷新文件列表
      if (local.onFileCreated) {
        local.onFileCreated();
      }

      // 重置状态
      handleCancelRename();
    } catch (error) {
      console.error('重命名文件失败:', error);
      toast.error('重命名失败，请重试');
    }
  };

  // 新建笔记
  const handleCreateNote = async () => {
    // 生成新笔记文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const fileName = `note_${timestamp}.md`;

    // 创建默认笔记内容
    const defaultContent = `# ${fileName.replace('.md', '')}

## 笔记内容

在这里开始编写您的笔记...

### 提示
- 支持Markdown语法
- 使用 Ctrl+S 保存
- 可以切换预览模式查看效果

---

*创建时间：${new Date().toLocaleString()}*`;

    try {
      // 立即上传文件到服务器
      const blob = new Blob([defaultContent], { type: 'text/markdown' });

      // 创建FormData并添加文件
      const formData = new FormData();
      formData.append('file', blob, fileName);

      // 直接使用fetch上传，避免File构造函数问题
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper?.id}/files?type=note`,
        {
          method: 'POST',
          body: formData
        }
      );

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      // 创建文件对象（不再标记为新建，因为已经上传）
      const newNoteFile = {
        name: fileName,
        size: blob.size,
        type: 'note',
        isNew: false // 已经上传，不再是新建文件
      };

      // 进入编辑模式
      setEditingFile(newNoteFile);
      setRightPanelView('editor');


      // 通知父组件刷新文件列表
      if (local.onFileCreated) {
        local.onFileCreated();
      }

      toast.success('笔记创建成功，开始编辑吧！');
    } catch (error) {
      console.error('创建笔记失败:', error);
      toast.error('创建笔记失败，请重试');
    }
  };

  // 处理图片上传
  const handleUploadImage = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;

    input.onchange = async (e) => {
      const files = Array.from(e.target.files);
      if (files.length === 0) return;

      try {
        for (const file of files) {
          // 检查文件类型
          if (!file.type.startsWith('image/')) {
            toast.error(`${file.name} 不是有效的图片文件`);
            continue;
          }

          // 检查文件大小（限制为10MB）
          if (file.size > 10 * 1024 * 1024) {
            toast.error(`${file.name} 文件过大，请选择小于10MB的图片`);
            continue;
          }

          // 创建FormData
          const formData = new FormData();
          formData.append('file', file);

          // 上传文件
          const response = await fetch(
            `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper.id}/files?type=image`,
            {
              method: 'POST',
              body: formData
            }
          );

          if (response.ok) {
            toast.success(`图片 ${file.name} 上传成功`);
          } else {
            throw new Error(`Failed to upload ${file.name}`);
          }
        }

        // 上传完成后刷新文件列表
        if (local.onFileCreated) {
          local.onFileCreated();
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        toast.error('图片上传失败，请重试');
      }
    };

    input.click();
  };

  // 处理删除文件
  const handleDeleteFile = async (file) => {
    // 确认删除
    const confirmed = window.confirm(`确定要删除文件 "${file.name}" 吗？此操作不可撤销。`);
    if (!confirmed) return;

    try {
      // 调用删除API
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper.id}/files/${encodeURIComponent(file.name)}`,
        {
          method: 'DELETE'
        }
      );

      if (response.ok) {
        toast.success(`文件 ${file.name} 删除成功`);

        // 如果删除的是当前正在编辑的文件，关闭编辑器
        if (editingFile()?.name === file.name) {
          handleCloseEditor();
        }

        // 刷新文件列表
        if (local.onFileCreated) {
          local.onFileCreated();
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '删除失败');
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      toast.error(`删除文件失败: ${error.message}`);
    }
  };





  // 监听Modal打开状态，强制重新创建PDF查看器
  createEffect(() => {
    if (local.open) {
      // 每次Modal打开时生成新的key，强制重新创建PdfViewer
      setPdfViewerKey(Date.now());
      console.log('Modal opened, forcing PDF viewer recreation with key:', Date.now());
    }
  });

  // 键盘事件监听
  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);

    // 监听全屏变化
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
  });

  onCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('fullscreenchange', () => { });
  });

  return (
    <Show when={local.open}>
      <div
        ref={containerRef}
        class="fixed inset-0 z-50 bg-black bg-opacity-95 flex"
        {...others}
      >
        {/* 左侧PDF预览区域 */}
        <div
          class="flex flex-col"
          style={{ width: `${leftPanelWidth()}%` }}
        >

          {/* 使用Show组件和key确保每次Modal打开都重新创建PdfViewer实例 */}
          <Show when={local.open && pdfUrl()}>
            <div key={pdfViewerKey()}>
              <PdfViewer
                pdfUrl={pdfUrl()}
                paperId={local.paper?.id}
                originalFileName={local.originFile?.name}
                onClose={local.onClose}
              />
            </div>
          </Show>

        </div>

        {/* 拖拽分隔条 */}
        <div
          class={`w-1 bg-gray-600 cursor-col-resize hover:bg-gray-500 transition-colors ${isDragging() ? 'bg-blue-500' : ''
            }`}
          onMouseDown={startDrag}
          title="拖拽调整面板宽度"
        />

        {/* 右侧面板区域 */}
        <div
          class="flex flex-col bg-white dark:bg-gray-900"
          style={{ width: `${100 - leftPanelWidth()}%` }}
        >
          <Show when={rightPanelView() === 'files'}>
            {/* 文件列表工具栏 */}
            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                文件附件
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={local.onClose}
                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X size={20} />
              </Button>
            </div>
          </Show>

          <Show when={rightPanelView() === 'editor'}>
            {/* 编辑器视图 */}
            <MarkdownEditor
              file={editingFile()}
              paperId={local.paper?.id}
              onClose={handleCloseEditor}
              onFileDownload={local.onFileDownload}
              imageFiles={local.imageFiles}
              onFileCreated={() => {
                // 文件创建/上传成功后刷新文件列表
                if (local.onFileCreated) {
                  local.onFileCreated();
                }
              }}
              onFileRenamed={() => {
                // 重命名成功后刷新文件列表
                if (local.onFileCreated) {
                  local.onFileCreated();
                }
              }}
            />
          </Show>

          <Show when={rightPanelView() === 'files'}>
            {/* 文件列表内容 */}
            <div class="flex-1 overflow-auto p-3 space-y-4">
              {/* 笔记文件 */}
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <File size={16} class="text-orange-500" />
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white">笔记文件</h4>
                    <Badge variant="warning" size="sm">{local.noteFiles?.length || 0}</Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCreateNote}
                    class="h-7 px-2 text-orange-600 border-orange-300 hover:bg-orange-50 dark:text-orange-400 dark:border-orange-600 dark:hover:bg-orange-900/20"
                    title="新建笔记"
                  >
                    <Plus size={14} class="mr-1" />
                    新建
                  </Button>
                </div>

                <Show when={(local.noteFiles?.length || 0) > 0}>
                  <div class="space-y-1.5">
                    <For each={local.noteFiles}>
                      {(file) => (
                        <div class="flex items-center justify-between p-2 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-md border border-orange-200 dark:border-orange-700 hover:shadow-sm transition-all duration-200">
                          <div
                            class="flex items-center space-x-2 min-w-0 flex-1 cursor-pointer"
                            onClick={() => handleNoteFileEdit(file)}
                            title="点击编辑文件"
                          >
                            <div class="p-1.5 bg-orange-100 dark:bg-orange-800 rounded-md flex-shrink-0">
                              <File size={12} class="text-orange-600 dark:text-orange-300" />
                            </div>
                            <div class="min-w-0 flex-1">
                              <Show when={renamingFile() === file}>
                                {/* 重命名输入框 */}
                                <div
                                  class="flex items-center space-x-1"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Input
                                    value={newFileName()}
                                    onInput={(e) => setNewFileName(e.target.value)}
                                    onKeyPress={handleRenameKeyPress}
                                    onClick={(e) => e.stopPropagation()}
                                    class="text-xs h-6 px-2 flex-1"
                                    placeholder="输入新文件名"
                                    autofocus
                                  />
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleConfirmRename();
                                    }}
                                    class="h-6 w-6 p-1 text-green-600 hover:text-green-700"
                                    title="确认重命名"
                                  >
                                    ✓
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCancelRename();
                                    }}
                                    class="h-6 w-6 p-1 text-gray-500 hover:text-gray-700"
                                    title="取消重命名"
                                  >
                                    ✕
                                  </Button>
                                </div>
                              </Show>
                              <Show when={renamingFile() !== file}>
                                {/* 正常显示文件名 */}
                                <p class="text-xs font-medium text-gray-900 dark:text-white truncate">{file.name}</p>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                  {file.size ? `${Math.round(file.size / 1024)} KB` : '未知大小'}
                                </span>
                              </Show>
                            </div>
                          </div>
                          <Show when={renamingFile() !== file}>
                            <div class="flex items-center space-x-1">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleNoteFileEdit(file);
                                }}
                                class="flex-shrink-0 h-6 px-2 text-xs text-blue-600 hover:text-blue-700"
                                title="编辑文件"
                              >
                                编辑
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStartRename(file);
                                }}
                                class="flex-shrink-0 h-6 px-2 text-xs text-orange-600 hover:text-orange-700"
                                title="重命名文件"
                              >
                                重命名
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  local.onFileDownload?.(file);
                                }}
                                class="flex-shrink-0 h-6 px-2 text-xs text-green-600 hover:text-green-700"
                                title="下载文件"
                              >
                                下载
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteFile(file);
                                }}
                                class="flex-shrink-0 h-6 px-2 text-xs text-red-600 hover:text-red-700"
                                title="删除文件"
                              >
                                删除
                              </Button>
                            </div>
                          </Show>
                        </div>
                      )}
                    </For>
                  </div>
                </Show>

                <Show when={(local.noteFiles?.length || 0) === 0}>
                  <div class="p-4 text-center bg-orange-50 dark:bg-orange-900/20 rounded-lg border-2 border-dashed border-orange-200 dark:border-orange-700">
                    <File size={24} class="mx-auto text-orange-400 mb-2" />
                    <p class="text-sm text-orange-600 dark:text-orange-300">暂无笔记文件</p>
                  </div>
                </Show>
              </div>

              {/* 图片文件 */}
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <ImageIcon size={16} class="text-green-500" />
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white">图片文件</h4>
                    <Badge variant="success" size="sm">{local.imageFiles?.length || 0}</Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleUploadImage}
                    class="h-7 px-2 text-green-600 border-green-300 hover:bg-green-50 dark:text-green-400 dark:border-green-600 dark:hover:bg-green-900/20"
                    title="上传图片"
                  >
                    <Plus size={14} class="mr-1" />
                    上传
                  </Button>
                </div>

                <Show when={(local.imageFiles?.length || 0) > 0}>
                  <div class="flex flex-wrap gap-3">
                    <For each={local.imageFiles}>
                      {(file) => (
                        <div class="relative group">
                          <div
                            class="w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-green-500 transition-all flex-shrink-0 shadow-sm"
                            onClick={() => handleImagePreview(file, local.imageFiles)}
                            title="点击预览图片"
                          >
                            <img
                              src={`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5173'}/api/papers/${local.paper.id}/files/${file.name}`}
                              alt={file.name}
                              class="w-full h-full object-cover"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextElementSibling.style.display = 'flex';
                              }}
                            />
                            <div class="w-full h-full hidden items-center justify-center">
                              <ImageIcon size={20} class="text-gray-400" />
                            </div>
                          </div>
                          <div class="absolute inset-x-0 bottom-0 bg-black bg-opacity-70 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                            <p class="truncate text-xs font-medium">{file.name}</p>
                            <p class="text-xs opacity-80">{file.size ? `${Math.round(file.size / 1024)} KB` : '未知'}</p>
                          </div>
                          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                local.onFileDownload?.(file);
                              }}
                              class="bg-black bg-opacity-60 text-white hover:bg-opacity-80 p-1.5 h-7 w-7 rounded-md"
                              title="下载图片"
                            >
                              <Download size={12} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteFile(file);
                              }}
                              class="bg-red-600 bg-opacity-80 text-white hover:bg-opacity-100 p-1.5 h-7 w-7 rounded-md"
                              title="删除图片"
                            >
                              <Trash2 size={12} />
                            </Button>
                          </div>
                        </div>
                      )}
                    </For>
                  </div>
                </Show>

                <Show when={(local.imageFiles?.length || 0) === 0}>
                  <div class="p-4 text-center bg-green-50 dark:bg-green-900/20 rounded-lg border-2 border-dashed border-green-200 dark:border-green-700">
                    <ImageIcon size={24} class="mx-auto text-green-400 mb-2" />
                    <p class="text-sm text-green-600 dark:text-green-300">暂无图片文件</p>
                  </div>
                </Show>
              </div>
            </div>
          </Show>
        </div>

        {/* 图片预览组件 */}
        <ImagePreview
          open={showImagePreview()}
          onClose={() => setShowImagePreview(false)}
          images={previewImages()}
          currentIndex={currentImageIndex()}
          onIndexChange={setCurrentImageIndex}
          title="图片预览"
          showDownload={true}
          onDownload={(image) => {
            if (image.file) {
              local.onFileDownload?.(image.file);
            }
          }}
        />
      </div>
    </Show>
  );
};

export default PdfSplitViewModal;
