/**
 * PDF文本选择测试辅助工具
 * 用于验证PDF保存后文本选择功能是否正常
 */

export class PDFTestHelper {
  /**
   * 测试PDF文本选择功能
   * @param {HTMLElement} pdfContainer - PDF容器元素
   * @returns {Promise<boolean>} 测试结果
   */
  static async testTextSelection(pdfContainer) {
    try {
      console.log('开始测试PDF文本选择功能...');
      
      // 等待PDF加载完成
      await this.waitForPDFLoad(pdfContainer);
      
      // 查找文本层
      const textLayer = pdfContainer.querySelector('.textLayer');
      if (!textLayer) {
        console.error('未找到PDF文本层');
        return false;
      }
      
      console.log('找到PDF文本层:', textLayer);
      
      // 查找文本元素
      const textElements = textLayer.querySelectorAll('span');
      if (textElements.length === 0) {
        console.error('文本层中没有找到文本元素');
        return false;
      }
      
      console.log(`找到 ${textElements.length} 个文本元素`);
      
      // 测试文本选择
      const testResult = this.testTextSelectionInteraction(textElements[0]);
      
      if (testResult) {
        console.log('✅ PDF文本选择功能正常');
      } else {
        console.log('❌ PDF文本选择功能异常');
      }
      
      return testResult;
    } catch (error) {
      console.error('PDF文本选择测试失败:', error);
      return false;
    }
  }
  
  /**
   * 等待PDF加载完成
   * @param {HTMLElement} container - PDF容器
   * @param {number} timeout - 超时时间(ms)
   */
  static async waitForPDFLoad(container, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkLoad = () => {
        const textLayer = container.querySelector('.textLayer');
        const textElements = textLayer?.querySelectorAll('span');
        
        if (textElements && textElements.length > 0) {
          console.log('PDF加载完成');
          resolve();
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error('PDF加载超时'));
          return;
        }
        
        setTimeout(checkLoad, 100);
      };
      
      checkLoad();
    });
  }
  
  /**
   * 测试文本选择交互
   * @param {HTMLElement} textElement - 文本元素
   */
  static testTextSelectionInteraction(textElement) {
    try {
      // 模拟鼠标选择
      const rect = textElement.getBoundingClientRect();
      
      // 创建鼠标事件
      const mouseDownEvent = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
        clientX: rect.left + 5,
        clientY: rect.top + rect.height / 2
      });
      
      const mouseMoveEvent = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        clientX: rect.right - 5,
        clientY: rect.top + rect.height / 2
      });
      
      const mouseUpEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true,
        clientX: rect.right - 5,
        clientY: rect.top + rect.height / 2
      });
      
      // 触发事件
      textElement.dispatchEvent(mouseDownEvent);
      textElement.dispatchEvent(mouseMoveEvent);
      textElement.dispatchEvent(mouseUpEvent);
      
      // 检查是否有文本被选中
      const selection = window.getSelection();
      const hasSelection = selection && selection.toString().length > 0;
      
      console.log('文本选择测试结果:', {
        hasSelection,
        selectedText: selection?.toString(),
        textElement: textElement.textContent
      });
      
      // 清除选择
      if (selection) {
        selection.removeAllRanges();
      }
      
      return hasSelection;
    } catch (error) {
      console.error('文本选择交互测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取PDF调试信息
   * @param {HTMLElement} container - PDF容器
   */
  static getPDFDebugInfo(container) {
    const info = {
      container: !!container,
      textLayer: !!container?.querySelector('.textLayer'),
      textElements: container?.querySelectorAll('.textLayer span').length || 0,
      annotationLayer: !!container?.querySelector('.annotationLayer'),
      canvasLayer: !!container?.querySelector('canvas'),
      timestamp: new Date().toISOString()
    };
    
    console.log('PDF调试信息:', info);
    return info;
  }
}

// 在开发环境下将测试工具挂载到window对象
if (import.meta.env.DEV) {
  window.PDFTestHelper = PDFTestHelper;
}
