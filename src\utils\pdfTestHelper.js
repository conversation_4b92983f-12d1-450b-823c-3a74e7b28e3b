/**
 * PDF文本选择测试辅助工具
 * 用于验证PDF保存后文本选择功能是否正常
 */

export class PDFTestHelper {
  /**
   * 测试PDF文本选择功能
   * @param {HTMLElement} pdfContainer - PDF容器元素
   * @returns {Promise<boolean>} 测试结果
   */
  static async testTextSelection(pdfContainer) {
    try {
      console.log('开始测试PDF文本选择功能...', { pdfContainer, type: typeof pdfContainer });

      // 验证容器参数
      if (!pdfContainer) {
        console.error('PDF容器参数为空');
        return false;
      }

      // 检查是否为DOM元素
      if (typeof pdfContainer.querySelector !== 'function') {
        console.error('传入的参数不是有效的DOM元素:', pdfContainer);

        // 尝试从document查找PDF容器
        const fallbackContainer = document.querySelector('.pdfViewer') ||
                                 document.querySelector('[class*="pdf"]') ||
                                 document.querySelector('.PDFSlickViewer');

        if (fallbackContainer) {
          console.log('使用备用PDF容器:', fallbackContainer);
          pdfContainer = fallbackContainer;
        } else {
          console.error('无法找到PDF容器元素');
          return false;
        }
      }

      // 等待PDF加载完成
      await this.waitForPDFLoad(pdfContainer);

      // 查找文本层
      const textLayer = pdfContainer.querySelector('.textLayer');
      if (!textLayer) {
        console.error('未找到PDF文本层');
        // 尝试在整个文档中查找
        const globalTextLayer = document.querySelector('.textLayer');
        if (globalTextLayer) {
          console.log('在全局范围找到PDF文本层:', globalTextLayer);
          return this.testTextSelectionOnLayer(globalTextLayer);
        }
        return false;
      }

      console.log('找到PDF文本层:', textLayer);
      return this.testTextSelectionOnLayer(textLayer);

    } catch (error) {
      console.error('PDF文本选择测试失败:', error);
      return false;
    }
  }

  /**
   * 在指定的文本层上测试文本选择
   * @param {HTMLElement} textLayer - 文本层元素
   * @returns {boolean} 测试结果
   */
  static testTextSelectionOnLayer(textLayer) {
    try {
      // 查找文本元素
      const textElements = textLayer.querySelectorAll('span');
      if (textElements.length === 0) {
        console.error('文本层中没有找到文本元素');
        return false;
      }

      console.log(`找到 ${textElements.length} 个文本元素`);

      // 测试文本选择
      const testResult = this.testTextSelectionInteraction(textElements[0]);

      if (testResult) {
        console.log('✅ PDF文本选择功能正常');
      } else {
        console.log('❌ PDF文本选择功能异常');
      }

      return testResult;
    } catch (error) {
      console.error('文本层测试失败:', error);
      return false;
    }
  }
  
  /**
   * 等待PDF加载完成
   * @param {HTMLElement} container - PDF容器
   * @param {number} timeout - 超时时间(ms)
   */
  static async waitForPDFLoad(container, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      let checkCount = 0;

      const checkLoad = () => {
        checkCount++;
        console.log(`PDF加载检查 #${checkCount}...`);

        try {
          // 尝试多种方式查找文本层
          let textLayer = container.querySelector('.textLayer');

          if (!textLayer) {
            // 尝试在全局查找
            textLayer = document.querySelector('.textLayer');
          }

          if (!textLayer) {
            // 尝试查找PDFSlick特定的文本层
            textLayer = container.querySelector('[class*="textLayer"]') ||
                       document.querySelector('[class*="textLayer"]');
          }

          const textElements = textLayer?.querySelectorAll('span');

          console.log('PDF加载状态检查:', {
            textLayer: !!textLayer,
            textElements: textElements?.length || 0,
            container: container.className || 'no-class'
          });

          if (textElements && textElements.length > 0) {
            console.log('PDF加载完成，找到文本元素:', textElements.length);
            resolve();
            return;
          }

          if (Date.now() - startTime > timeout) {
            console.error('PDF加载超时，最终状态:', {
              textLayer: !!textLayer,
              textElements: textElements?.length || 0,
              checkCount,
              timeout
            });
            reject(new Error(`PDF加载超时 (${timeout}ms)`));
            return;
          }

          setTimeout(checkLoad, 200);
        } catch (error) {
          console.error('PDF加载检查出错:', error);
          setTimeout(checkLoad, 200);
        }
      };

      checkLoad();
    });
  }
  
  /**
   * 测试文本选择交互
   * @param {HTMLElement} textElement - 文本元素
   */
  static testTextSelectionInteraction(textElement) {
    try {
      // 模拟鼠标选择
      const rect = textElement.getBoundingClientRect();
      
      // 创建鼠标事件
      const mouseDownEvent = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
        clientX: rect.left + 5,
        clientY: rect.top + rect.height / 2
      });
      
      const mouseMoveEvent = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        clientX: rect.right - 5,
        clientY: rect.top + rect.height / 2
      });
      
      const mouseUpEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true,
        clientX: rect.right - 5,
        clientY: rect.top + rect.height / 2
      });
      
      // 触发事件
      textElement.dispatchEvent(mouseDownEvent);
      textElement.dispatchEvent(mouseMoveEvent);
      textElement.dispatchEvent(mouseUpEvent);
      
      // 检查是否有文本被选中
      const selection = window.getSelection();
      const hasSelection = selection && selection.toString().length > 0;
      
      console.log('文本选择测试结果:', {
        hasSelection,
        selectedText: selection?.toString(),
        textElement: textElement.textContent
      });
      
      // 清除选择
      if (selection) {
        selection.removeAllRanges();
      }
      
      return hasSelection;
    } catch (error) {
      console.error('文本选择交互测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取PDF调试信息
   * @param {HTMLElement} container - PDF容器
   */
  static getPDFDebugInfo(container) {
    const info = {
      container: !!container,
      textLayer: !!container?.querySelector('.textLayer'),
      textElements: container?.querySelectorAll('.textLayer span').length || 0,
      annotationLayer: !!container?.querySelector('.annotationLayer'),
      canvasLayer: !!container?.querySelector('canvas'),
      timestamp: new Date().toISOString()
    };
    
    console.log('PDF调试信息:', info);
    return info;
  }

  /**
   * 简单的PDF文本选择测试 - 不依赖特定容器
   * @returns {Promise<boolean>} 测试结果
   */
  static async testTextSelectionSimple() {
    try {
      console.log('开始简单PDF文本选择测试...');

      // 直接在全局查找文本层
      const textLayer = document.querySelector('.textLayer');
      if (!textLayer) {
        console.log('未找到PDF文本层，尝试等待...');

        // 等待一段时间后再次尝试
        await new Promise(resolve => setTimeout(resolve, 1000));

        const textLayerRetry = document.querySelector('.textLayer');
        if (!textLayerRetry) {
          console.error('仍未找到PDF文本层');
          return false;
        }

        return this.testTextSelectionOnLayer(textLayerRetry);
      }

      return this.testTextSelectionOnLayer(textLayer);
    } catch (error) {
      console.error('简单PDF文本选择测试失败:', error);
      return false;
    }
  }
}

// 在开发环境下将测试工具挂载到window对象
if (import.meta.env.DEV) {
  window.PDFTestHelper = PDFTestHelper;
}
