import { createMemo, For, splitProps } from 'solid-js';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-solid';
import Select from './Select';
import NumberInput from './NumberInput';

const Pagination = (props) => {
  const [local, others] = splitProps(props, [
    'current',
    'total',
    'pageSize',
    'showSizeChanger',
    'showQuickJumper',
    'showTotal',
    'onChange',
    'onShowSizeChange',
    'pageSizeOptions',
    'size',
    'class'
  ]);

  const pageSize = () => local.pageSize || 10;
  const current = () => local.current || 1;
  const total = () => local.total || 0;
  
  const totalPages = createMemo(() => Math.ceil(total() / pageSize()));
  
  const pageSizeOptions = () => local.pageSizeOptions || [10, 20, 50, 100];
  
  const size = local.size || 'md';

  const sizes = {
    sm: {
      button: 'px-2 py-1 text-xs',
      input: 'px-2 py-1 text-xs w-12',
      select: 'px-2 py-1 text-xs'
    },
    md: {
      button: 'px-3 py-2 text-sm',
      input: 'px-3 py-2 text-sm w-16',
      select: 'px-3 py-2 text-sm'
    },
    lg: {
      button: 'px-4 py-2 text-base',
      input: 'px-4 py-2 text-base w-20',
      select: 'px-4 py-2 text-base'
    }
  };

  // 生成页码数组
  const getPageNumbers = createMemo(() => {
    const pages = [];
    const currentPage = current();
    const totalPageCount = totalPages();
    
    if (totalPageCount <= 7) {
      // 如果总页数小于等于7，显示所有页码
      for (let i = 1; i <= totalPageCount; i++) {
        pages.push(i);
      }
    } else {
      // 复杂的页码逻辑
      if (currentPage <= 4) {
        // 当前页在前面
        pages.push(1, 2, 3, 4, 5, '...', totalPageCount);
      } else if (currentPage >= totalPageCount - 3) {
        // 当前页在后面
        pages.push(1, '...', totalPageCount - 4, totalPageCount - 3, totalPageCount - 2, totalPageCount - 1, totalPageCount);
      } else {
        // 当前页在中间
        pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPageCount);
      }
    }
    
    return pages;
  });

  const handlePageChange = (page) => {
    if (page !== current() && page >= 1 && page <= totalPages() && local.onChange) {
      local.onChange(page, pageSize());
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    if (local.onShowSizeChange) {
      local.onShowSizeChange(current(), newPageSize);
    }
  };



  if (totalPages() <= 1) {
    return null;
  }

  return (
    <div class={`flex items-center justify-between ${local.class || ''}`} {...others}>
      {/* 总数显示 */}
      {local.showTotal && (
        <div class="text-sm text-theme-secondary">
          共 {total()} 条记录
        </div>
      )}

      <div class="flex items-center space-x-2">
        {/* 页面大小选择器 */}
        {local.showSizeChanger && (
          <div class="flex items-center space-x-2">
            <span class="text-sm text-theme-secondary">每页</span>
            <Select
              value={pageSize()}
              onSelectionChange={(value) => handlePageSizeChange(parseInt(value))}
              options={pageSizeOptions().map(option => ({ value: option, label: option }))}
              size="sm"
              class="w-20"
            />
            <span class="text-sm text-theme-secondary">条</span>
          </div>
        )}

        {/* 分页按钮 */}
        <div class="flex items-center space-x-1">
          {/* 上一页 */}
          <button
            onClick={() => handlePageChange(current() - 1)}
            disabled={current() === 1}
            class={`
              ${sizes[size].button}
              btn-outline-theme border rounded
              disabled:opacity-50 disabled:cursor-not-allowed
              flex items-center
            `}
          >
            <ChevronLeft size={16} />
          </button>

          {/* 页码 */}
          <For each={getPageNumbers()}>
            {(page) => (
              page === '...' ? (
                <span class={`${sizes[size].button} text-theme-muted`}>
                  <MoreHorizontal size={16} />
                </span>
              ) : (
                <button
                  onClick={() => handlePageChange(page)}
                  class={`
                    ${sizes[size].button}
                    border rounded transition-colors
                    ${current() === page
                      ? 'btn-primary-theme'
                      : 'btn-outline-theme border'
                    }
                  `}
                >
                  {page}
                </button>
              )
            )}
          </For>

          {/* 下一页 */}
          <button
            onClick={() => handlePageChange(current() + 1)}
            disabled={current() === totalPages()}
            class={`
              ${sizes[size].button}
              btn-outline-theme border rounded
              disabled:opacity-50 disabled:cursor-not-allowed
              flex items-center
            `}
          >
            <ChevronRight size={16} />
          </button>
        </div>

        {/* 快速跳转 */}
        {local.showQuickJumper && (
          <div class="flex items-center space-x-2">
            <span class="text-sm text-theme-secondary">跳至</span>
            <NumberInput
              min={1}
              max={totalPages()}
              onValueChange={(value) => {
                if (value && value >= 1 && value <= totalPages()) {
                  handlePageChange(value);
                }
              }}
              placeholder="页码"
              size={size}
              class="w-16"
              showControls={false}
              allowEmpty={true}
            />
            <span class="text-sm text-theme-secondary">页</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Pagination;
