import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?url';
import { usePDFSlick, ScrollMode } from "@pdfslick/solid";
import "@pdfslick/solid/dist/pdf_viewer.css";
import { createEffect } from 'solid-js';
import PDFToolbar from './PDFToolbar';

// 在开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('../../utils/testPdfFunctionality.js');
}

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default function PDFViewer(props) {
  const {
    viewerRef,
    pdfSlickStore: store,
    PDFSlickViewer,
  } = usePDFSlick(props.pdfUrl, {
    scaleValue: "page-fit",
    annotationMode: 1,
    textLayerMode: 1,
  });

  // 配置PDFSlick
  createEffect(() => {
    if (store.pdfSlick) {
      store.pdfSlick.setScrollMode(ScrollMode.WRAPPED);
    }
  });

  return (
    <div class='flex flex-col h-full'>
      <PDFToolbar
        store={store}
        paperId={props.paperId}
        originalFileName={props.originalFileName}
        onClose={props.onClose}
      />
      <div class='relative h-full'>
        <PDFSlickViewer {...{ store, viewerRef }} />
      </div>
    </div>
  );
}
