import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?url';
import { usePDFSlick, ScrollMode } from "@pdfslick/solid";
import "@pdfslick/solid/dist/pdf_viewer.css";
import { createEffect } from 'solid-js';
import PDFToolbar from './PDFToolbar';

// 在开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('../../utils/testPdfFunctionality.js');
}

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default function PDFViewer(props) {
  const {
    viewerRef,
    pdfSlickStore: store,
    PDFSlickViewer,
  } = usePDFSlick(props.pdfUrl, {
    scaleValue: "page-fit",
    textLayerMode: 2, // ENABLE_PERMISSIONS - 确保文本选择功能正常
    annotationMode: 2, // 启用注释模式
    annotationEditorMode: 0, // 默认不启用编辑模式，通过工具栏控制
  });

  // 配置PDFSlick
  createEffect(() => {
    if (store.pdfSlick) {
      store.pdfSlick.setScrollMode(ScrollMode.WRAPPED);

      // 确保文本层正确初始化
      if (store.pdfSlick.viewer) {
        // 强制启用文本层
        store.pdfSlick.viewer.textLayerMode = 2;

        // 在开发环境下添加调试信息
        if (import.meta.env.DEV) {
          console.log('PDFSlick配置:', {
            textLayerMode: store.pdfSlick.viewer.textLayerMode,
            annotationMode: store.pdfSlick.viewer.annotationMode,
            url: props.pdfUrl
          });

          // 延迟测试文本选择功能
          setTimeout(() => {
            if (window.testPdfFunctionality) {
              window.testPdfFunctionality();
            }
          }, 2000);
        }
      }
    }
  });

  return (
    <div class='flex flex-col h-full'>
      <PDFToolbar
        store={store}
        paperId={props.paperId}
        originalFileName={props.originalFileName}
        onClose={props.onClose}
      />
      <div class='relative h-full'>
        <PDFSlickViewer {...{ store, viewerRef }} />
      </div>
    </div>
  );
}
