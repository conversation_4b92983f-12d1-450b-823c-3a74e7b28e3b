import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?url'; // Vite/Rollup specific import
import { usePDFSlick, ScrollMode } from "@pdfslick/solid";
import "@pdfslick/solid/dist/pdf_viewer.css";
import { createEffect, onMount, onCleanup, createSignal } from 'solid-js';
import PDFToolbar from './PDFToolbar';
import { PDFTestHelper } from '../../utils/pdfTestHelper';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default function PDFViewer(props) {
  // 创建一个key信号来强制重新初始化PDFSlick
  const [viewerKey, setViewerKey] = createSignal(Date.now());
  const [lastUrl, setLastUrl] = createSignal(null);

  let {
    viewerRef,
    pdfSlickStore: store,
    PDFSlickViewer,
  } = usePDFSlick(props.pdfUrl, {
    annotationMode: 1,
    textLayerMode: 1,
  });

  // 监听PDF URL变化，强制重新初始化
  createEffect(() => {
    const currentUrl = props.pdfUrl;
    const previousUrl = lastUrl();

    if (currentUrl && currentUrl !== previousUrl) {
      console.log('PDF URL changed from', previousUrl, 'to', currentUrl);
      setLastUrl(currentUrl);

      // URL变化时增加key值，触发PDFSlick重新初始化
      setViewerKey(Date.now());

      // 如果已有PDFSlick实例，先清理
      if (store.pdfSlick?.viewer) {
        console.log('Cleaning up previous PDFSlick instance');
        try {
          store.pdfSlick.viewer.cleanup?.();
        } catch (e) {
          console.warn('Error during PDFSlick cleanup:', e);
        }
      }
    }
  });

  onMount(() => {
    console.log('PDFViewer mounted, store:', store);
    setLastUrl(props.pdfUrl);
  });

  // 确保PDFSlick正确配置
  createEffect(() => {
    if (store.pdfSlick) {
      // 设置滚动模式
      store.pdfSlick.setScrollMode(ScrollMode.WRAPPED);

      // 确保文本层正确启用
      if (store.pdfSlick.viewer) {
        store.pdfSlick.viewer.textLayerMode = 1;
        console.log('PDFSlick configured with text layer enabled');

        // 强制重新渲染文本层
        setTimeout(() => {
          if (store.pdfSlick.viewer.textLayerFactory) {
            console.log('Forcing text layer re-render');
          }

          // 在开发环境下自动测试文本选择功能
          if (import.meta.env.DEV && viewerRef) {
            setTimeout(() => {
              PDFTestHelper.testTextSelection(viewerRef)
                .then(result => {
                  console.log('自动文本选择测试结果:', result);
                })
                .catch(error => {
                  console.error('自动文本选择测试失败:', error);
                });
            }, 2000);
          }
        }, 1000);
      }
    }
  });

  // 清理资源
  onCleanup(() => {
    if (store.pdfSlick?.viewer) {
      console.log('Cleaning up PDFViewer resources');
      try {
        store.pdfSlick.viewer.cleanup?.();
      } catch (e) {
        console.warn('Error during cleanup:', e);
      }
    }
  });

  return (
    <div class='flex flex-col h-full' key={viewerKey()}>
      <PDFToolbar
        store={store}
        paperId={props.paperId}
        originalFileName={props.originalFileName}
        onPdfSaved={props.onPdfSaved}
      />
      <div class='relative h-full'>
        <PDFSlickViewer {...{ store, viewerRef }} />
      </div>
    </div>
  );

}
