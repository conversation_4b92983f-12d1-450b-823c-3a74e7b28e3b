import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?url'; // Vite/Rollup specific import
import { usePDFSlick, ScrollMode } from "@pdfslick/solid";
import "@pdfslick/solid/dist/pdf_viewer.css";
import { createEffect, onMount } from 'solid-js';
import PDFToolbar from './PDFToolbar';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export default function PDFViewer(props) {

  let {
    viewerRef,
    pdfSlickStore: store,
    PDFSlickViewer,
  } = usePDFSlick(props.pdfUrl, {
    annotationMode: 1,
    textLayerMode: 1,
  });

  onMount(() => {
    console.log(store);
  })

  createEffect(() => {
    if (store.pdfSlick) {
      store.pdfSlick.setScrollMode(ScrollMode.WRAPPED);
    }
  })

  return (
    <div class='flex flex-col h-full'>
      <PDFToolbar store={store} paperId={props.paperId} originalFileName={props.originalFileName} />
      <div class='relative h-full'>
        <PDFSlickViewer {...{ store, viewerRef }} />
      </div>
    </div>
  );

}
